<div class="dashboard-container">
  <!-- Toolbar -->
  <mat-toolbar color="primary" class="dashboard-toolbar">
    <span>Credential Manager</span>
    <span class="spacer"></span>
    <button mat-icon-button [matMenuTriggerFor]="userMenu">
      <mat-icon>account_circle</mat-icon>
    </button>
    <mat-menu #userMenu="matMenu">
      <button mat-menu-item (click)="signOut()">
        <mat-icon>logout</mat-icon>
        <span>Sign Out</span>
      </button>
    </mat-menu>
  </mat-toolbar>

  <!-- Main Content -->
  <div class="dashboard-content">
    <div class="dashboard-header">
      <h1>Your Credentials</h1>
      <div class="header-actions">
        <button mat-raised-button color="primary" (click)="validateAllApiKeys()"
                [disabled]="isValidatingKeys()">
          <mat-icon>verified</mat-icon>
          {{isValidatingKeys() ? 'Validating...' : 'Validate All API Keys'}}
        </button>
        <button mat-fab color="accent" class="add-button" (click)="openAddCredentialDialog()">
          <mat-icon>add</mat-icon>
        </button>
      </div>
    </div>

    <!-- Search Bar -->
    <div class="search-container">
      <mat-form-field appearance="outline" class="search-field">
        <mat-label>Search credentials...</mat-label>
        <input matInput
               [(ngModel)]="searchQuery"
               (ngModelChange)="onSearchChange($event)"
               placeholder="Search by title, provider, username, email, or description">
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>
    </div>

    <!-- Tabs -->
    <mat-tab-group [(selectedIndex)]="selectedTabIndex" class="credential-tabs">
      <!-- Passwords Tab -->
      <mat-tab label="Passwords">
        <div class="tab-content">
          <div class="credentials-grid" *ngIf="filteredPasswordCredentials().length > 0; else noPasswords">
            <mat-card *ngFor="let password of filteredPasswordCredentials()" class="credential-card">
              <mat-card-header>
                <mat-card-title>{{password.title}}</mat-card-title>
                <mat-card-subtitle>{{password.website || password.email}}</mat-card-subtitle>
              </mat-card-header>
              <mat-card-content>
                <div class="credential-info">
                  <p *ngIf="password.username"><strong>Username:</strong> {{password.username}}</p>
                  <p *ngIf="password.email"><strong>Email:</strong> {{password.email}}</p>
                  <p><strong>Password:</strong> ••••••••</p>
                  <p *ngIf="password.notes" class="notes">{{password.notes}}</p>
                </div>
              </mat-card-content>
              <mat-card-actions>
                <button mat-button color="primary">
                  <mat-icon>edit</mat-icon>
                  Edit
                </button>
                <button mat-button color="warn" (click)="deleteCredential(password)">
                  <mat-icon>delete</mat-icon>
                  Delete
                </button>
              </mat-card-actions>
            </mat-card>
          </div>
          <ng-template #noPasswords>
            <div class="empty-state">
              <mat-icon class="empty-icon">lock</mat-icon>
              <h3>No passwords saved</h3>
              <p>Start by adding your first password</p>
            </div>
          </ng-template>
        </div>
      </mat-tab>

      <!-- API Keys Tab -->
      <mat-tab label="API Keys">
        <div class="tab-content">
          <div *ngIf="getProviderNames().length > 0; else noApiKeys">
            <!-- Provider Groups -->
            <div *ngFor="let provider of getProviderNames()" class="provider-section">
              <div class="provider-header">
                <h3>{{provider}}</h3>
                <div class="provider-stats">
                  <mat-chip-set>
                    <mat-chip color="primary">
                      {{getValidKeysCount(provider)}}/{{getTotalKeysCount(provider)}} Valid
                    </mat-chip>
                  </mat-chip-set>
                  <button mat-button color="primary" (click)="validateProviderKeys(provider)">
                    <mat-icon>refresh</mat-icon>
                    Validate
                  </button>
                </div>
              </div>

              <div class="credentials-grid">
                <mat-card *ngFor="let apiKey of filteredGroupedApiKeys()[provider]" class="credential-card api-key-card">
                  <mat-card-header>
                    <mat-card-title>
                      {{apiKey.keyName || apiKey.title}}
                      <mat-chip [color]="getKeyStatusColor(apiKey)" class="status-chip">
                        {{getKeyStatusText(apiKey)}}
                      </mat-chip>
                    </mat-card-title>
                    <mat-card-subtitle>{{apiKey.description || apiKey.usage}}</mat-card-subtitle>
                  </mat-card-header>
                  <mat-card-content>
                    <div class="credential-info">
                      <p><strong>Provider:</strong> {{apiKey.provider}}</p>
                      <p><strong>API Key:</strong> {{apiKey.apiKey.substring(0, 8)}}••••••••</p>
                      <p *ngIf="apiKey.usage"><strong>Usage:</strong> {{apiKey.usage}}</p>
                      <p *ngIf="apiKey.lastValidated">
                        <strong>Last Validated:</strong> {{apiKey.lastValidated | date:'short'}}
                      </p>

                      <p *ngIf="apiKey.validationError" class="error-text">
                        <strong>Error:</strong> {{apiKey.validationError}}
                      </p>
                    </div>
                  </mat-card-content>
                  <mat-card-actions>
                    <button mat-button color="primary">
                      <mat-icon>edit</mat-icon>
                      Edit
                    </button>
                    <button mat-button color="accent">
                      <mat-icon>verified</mat-icon>
                      Test
                    </button>
                    <button mat-button color="warn" (click)="deleteCredential(apiKey)">
                      <mat-icon>delete</mat-icon>
                      Delete
                    </button>
                  </mat-card-actions>
                </mat-card>
              </div>
            </div>
          </div>

          <ng-template #noApiKeys>
            <div class="empty-state">
              <mat-icon class="empty-icon">vpn_key</mat-icon>
              <h3>No API keys saved</h3>
              <p>Start by adding your first API key</p>
            </div>
          </ng-template>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>
</div>
