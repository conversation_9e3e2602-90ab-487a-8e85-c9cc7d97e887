.dashboard-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.dashboard-toolbar {
  position: sticky;
  top: 0;
  z-index: 1000;

  .spacer {
    flex: 1 1 auto;
  }
}

.dashboard-content {
  flex: 1;
  padding: 32px;
  overflow-y: auto;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 64px);
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  h1 {
    margin: 0;
    color: #333;
  }

  .header-actions {
    display: flex;
    gap: 16px;
    align-items: center;

    .add-button {
      width: 56px;
      height: 56px;
    }
  }
}

.credential-tabs {
  .mat-mdc-tab-body-wrapper {
    padding-top: 24px;
  }
}

.tab-content {
  min-height: 400px;
}

.credentials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.credential-card {
  transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
  border-radius: 12px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  // Button styling within cards
  mat-card-actions {
    padding: 16px;
    gap: 8px;

    button {
      border-radius: 8px;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
      }

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
        margin-right: 4px;
      }
    }
  }

  mat-card-header {
    padding-bottom: 16px;

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;

      .status-chip {
        font-size: 12px;
        height: 24px;
      }
    }
  }

  .credential-info {
    p {
      margin: 8px 0;

      &.notes {
        font-style: italic;
        color: #666;
      }

      &.expiry-date {
        &.expired {
          color: #f44336;
          font-weight: 500;
        }
      }

      &.error-text {
        color: #f44336;
        font-size: 14px;
      }
    }
  }
}

.api-key-card {
  border-left: 4px solid #2196f3;

  &.invalid {
    border-left-color: #f44336;
  }

  &.unknown {
    border-left-color: #ff9800;
  }
}

.provider-section {
  margin-bottom: 32px;

  .provider-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px;
    background: #f5f5f5;
    border-radius: 8px;

    h3 {
      margin: 0;
      color: #333;
    }

    .provider-stats {
      display: flex;
      align-items: center;
      gap: 16px;
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 24px;
  text-align: center;
  color: #666;

  .empty-icon {
    font-size: 64px;
    width: 64px;
    height: 64px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  h3 {
    margin: 16px 0 8px 0;
    font-weight: 400;
  }

  p {
    margin: 0;
    opacity: 0.7;
  }
}

@media (max-width: 768px) {
  .dashboard-content {
    padding: 20px;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
    margin-bottom: 20px;

    h1 {
      text-align: center;
      font-size: 24px;
    }

    .header-actions {
      justify-content: center;
      flex-wrap: wrap;
      gap: 12px;

      button {
        flex: 1;
        min-width: 140px;

        &.add-button {
          flex: none;
          width: 56px;
          height: 56px;
        }
      }
    }
  }

  .credentials-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .provider-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    padding: 16px;

    h3 {
      text-align: center;
      font-size: 18px;
    }

    .provider-stats {
      justify-content: center;
      flex-wrap: wrap;
      gap: 12px;
    }
  }

  .credential-card {
    mat-card-actions {
      flex-direction: column;
      gap: 8px;

      button {
        width: 100%;
        justify-content: center;
      }
    }
  }
}

@media (max-width: 480px) {
  .dashboard-content {
    padding: 16px;
  }

  .dashboard-header {
    gap: 16px;

    h1 {
      font-size: 20px;
    }
  }

  .credentials-grid {
    gap: 12px;
  }

  .provider-section {
    margin-bottom: 24px;
  }
}