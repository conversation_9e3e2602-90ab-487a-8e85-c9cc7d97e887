import { Component, inject, signal, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatMenuModule } from '@angular/material/menu';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

import { AuthService } from '../../services/auth.service';
import { CredentialService } from '../../services/credential.service';
import { ApiValidationService } from '../../services/api-validation.service';
import { CredentialType, PasswordCredential, ApiKeyCredential } from '../../models/credential.model';
import { AddCredential } from '../add-credential/add-credential';

@Component({
  selector: 'app-dashboard',
  imports: [
    CommonModule,
    FormsModule,
    MatToolbarModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatCardModule,
    MatChipsModule,
    MatMenuModule,
    MatSnackBarModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule
  ],
  templateUrl: './dashboard.html',
  styleUrl: './dashboard.scss'
})
export class Dashboard implements OnInit {
  private authService = inject(AuthService);
  private credentialService = inject(CredentialService);
  private apiValidationService = inject(ApiValidationService);
  private router = inject(Router);
  private snackBar = inject(MatSnackBar);
  private dialog = inject(MatDialog);

  credentials = signal<CredentialType[]>([]);
  passwordCredentials = signal<PasswordCredential[]>([]);
  apiKeyCredentials = signal<ApiKeyCredential[]>([]);
  groupedApiKeys = signal<{[provider: string]: ApiKeyCredential[]}>({});

  // Search functionality
  searchQuery = '';
  filteredPasswordCredentials = signal<PasswordCredential[]>([]);
  filteredApiKeyCredentials = signal<ApiKeyCredential[]>([]);
  filteredGroupedApiKeys = signal<{[provider: string]: ApiKeyCredential[]}>({});

  selectedTabIndex = signal(0);
  isValidatingKeys = signal(false);

  ngOnInit() {
    console.log('Dashboard ngOnInit called');
    this.credentialService.credentials$.subscribe(creds => {
      console.log('Dashboard received credentials:', creds);
      this.credentials.set(creds);

      const passwords = this.credentialService.getPasswordCredentials();
      const apiKeys = this.credentialService.getApiKeyCredentials();

      console.log('Dashboard passwords:', passwords);
      console.log('Dashboard API keys:', apiKeys);

      this.passwordCredentials.set(passwords);
      this.apiKeyCredentials.set(apiKeys);
      this.groupApiKeysByProvider();
      this.filterCredentials();
    });
  }

  private groupApiKeysByProvider() {
    const apiKeys = this.apiKeyCredentials();
    const grouped: {[provider: string]: ApiKeyCredential[]} = {};

    apiKeys.forEach(key => {
      if (!grouped[key.provider]) {
        grouped[key.provider] = [];
      }
      grouped[key.provider].push(key);
    });

    this.groupedApiKeys.set(grouped);
  }

  onSearchChange(query: string) {
    this.searchQuery = query.toLowerCase();
    this.filterCredentials();
  }

  private filterCredentials() {
    const query = this.searchQuery;

    if (!query) {
      // No search query, show all credentials
      this.filteredPasswordCredentials.set(this.passwordCredentials());
      this.filteredApiKeyCredentials.set(this.apiKeyCredentials());
      this.filteredGroupedApiKeys.set(this.groupedApiKeys());
    } else {
      // Filter password credentials
      const filteredPasswords = this.passwordCredentials().filter(cred =>
        cred.title.toLowerCase().includes(query) ||
        (cred.username && cred.username.toLowerCase().includes(query)) ||
        (cred.email && cred.email.toLowerCase().includes(query)) ||
        (cred.website && cred.website.toLowerCase().includes(query)) ||
        (cred.notes && cred.notes.toLowerCase().includes(query))
      );
      this.filteredPasswordCredentials.set(filteredPasswords);

      // Filter API key credentials
      const filteredApiKeys = this.apiKeyCredentials().filter(cred =>
        cred.title.toLowerCase().includes(query) ||
        cred.provider.toLowerCase().includes(query) ||
        (cred.keyName && cred.keyName.toLowerCase().includes(query)) ||
        (cred.description && cred.description.toLowerCase().includes(query)) ||
        (cred.usage && cred.usage.toLowerCase().includes(query)) ||
        (cred.endpoint && cred.endpoint.toLowerCase().includes(query))
      );
      this.filteredApiKeyCredentials.set(filteredApiKeys);

      // Group filtered API keys by provider
      const filteredGrouped: {[provider: string]: ApiKeyCredential[]} = {};
      filteredApiKeys.forEach(key => {
        if (!filteredGrouped[key.provider]) {
          filteredGrouped[key.provider] = [];
        }
        filteredGrouped[key.provider].push(key);
      });
      this.filteredGroupedApiKeys.set(filteredGrouped);
    }
  }

  async signOut() {
    try {
      await this.authService.signOut();
    } catch (error: any) {
      this.snackBar.open('Error signing out', 'Close', { duration: 3000 });
    }
  }

  async validateAllApiKeys() {
    this.isValidatingKeys.set(true);
    try {
      const apiKeys = this.apiKeyCredentials();
      const results = await this.apiValidationService.validateMultipleKeys(apiKeys);

      // Update credentials with validation results
      for (const [keyId, result] of Object.entries(results)) {
        await this.credentialService.updateCredential(keyId, {
          isValid: result.isValid,
          lastValidated: new Date(),
          validationError: result.error
        });
      }

      this.snackBar.open('API keys validated successfully', 'Close', { duration: 3000 });
    } catch (error) {
      this.snackBar.open('Error validating API keys', 'Close', { duration: 3000 });
    } finally {
      this.isValidatingKeys.set(false);
    }
  }

  async validateProviderKeys(provider: string) {
    const providerKeys = this.groupedApiKeys()[provider];
    if (!providerKeys?.length) return;

    try {
      const results = await this.apiValidationService.validateKeysByProvider(provider, providerKeys);

      // Update each key with validation result
      for (const key of [...results.validKeys, ...results.invalidKeys]) {
        if (key.id) {
          await this.credentialService.updateCredential(key.id, {
            isValid: results.validKeys.includes(key),
            lastValidated: new Date(),
            validationError: results.invalidKeys.includes(key) ? 'Validation failed' : undefined
          });
        }
      }

      this.snackBar.open(`${provider} keys validated`, 'Close', { duration: 3000 });
    } catch (error) {
      this.snackBar.open(`Error validating ${provider} keys`, 'Close', { duration: 3000 });
    }
  }

  getProviderNames(): string[] {
    return Object.keys(this.filteredGroupedApiKeys());
  }

  getValidKeysCount(provider: string): number {
    return this.filteredGroupedApiKeys()[provider]?.filter(key => key.isValid === true).length || 0;
  }

  getTotalKeysCount(provider: string): number {
    return this.filteredGroupedApiKeys()[provider]?.length || 0;
  }

  getKeyStatusColor(key: ApiKeyCredential): string {
    if (key.isValid === true) return 'primary';
    if (key.isValid === false) return 'warn';
    return 'accent';
  }

  getKeyStatusText(key: ApiKeyCredential): string {
    if (key.isValid === true) return 'Valid';
    if (key.isValid === false) return 'Invalid';
    return 'Unknown';
  }



  async copyToClipboard(text: string, type: string) {
    try {
      await navigator.clipboard.writeText(text);
      this.snackBar.open(`${type} copied to clipboard!`, 'Close', { duration: 2000 });
    } catch (error) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      this.snackBar.open(`${type} copied to clipboard!`, 'Close', { duration: 2000 });
    }
  }

  async deleteCredential(credential: CredentialType) {
    if (credential.id && confirm('Are you sure you want to delete this credential?')) {
      try {
        await this.credentialService.deleteCredential(credential.id);
        this.snackBar.open('Credential deleted successfully', 'Close', { duration: 3000 });
      } catch (error) {
        this.snackBar.open('Error deleting credential', 'Close', { duration: 3000 });
      }
    }
  }

  openAddCredentialDialog() {
    const dialogRef = this.dialog.open(AddCredential, {
      width: '600px',
      maxWidth: '95vw',
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Credential was added successfully
        console.log('Credential added');
      }
    });
  }
}
